import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { 
  updateAllWalletBalances, 
  updateNetworkWalletBalances, 
  getWalletBalanceHistory 
} from '../services/balance-update.service';
import { successResponse, errorResponse } from '../utils/common';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Manually trigger balance update for all wallets
 */
const updateAllBalances = catchAsync(async (req, res) => {
  const result = await updateAllWalletBalances();
  
  if (result.success) {
    successResponse(res, result.message, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Update balances for a specific network
 */
const updateNetworkBalances = catchAsync(async (req, res) => {
  const { networkId } = req.params;
  
  if (!networkId) {
    return errorResponse(res, 'Network ID is required');
  }
  
  // Get network details
  const network = await prisma.networks.findUnique({
    where: { id: parseInt(networkId) }
  });
  
  if (!network) {
    return errorResponse(res, 'Network not found');
  }
  
  const result = await updateNetworkWalletBalances(network);
  
  if (result.success) {
    successResponse(res, `Balance update completed for network ${network.name}`, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Get balance for a specific wallet address
 */
const getWalletBalance = catchAsync(async (req, res) => {
  const { address } = req.params;
  
  if (!address) {
    return errorResponse(res, 'Wallet address is required');
  }
  
  const result = await getWalletBalanceHistory(address);
  
  if (result.success) {
    successResponse(res, result.message, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Get all wallet balances with pagination
 */
const getAllWalletBalances = catchAsync(async (req, res) => {
  const { page = 1, limit = 50, network_id, coin_type } = req.query;
  
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
  const take = parseInt(limit as string);
  
  // Build where clause
  const where: any = {};
  if (network_id) {
    where.network_id = parseInt(network_id as string);
  }
  if (coin_type) {
    where.coin_type = coin_type as string;
  }
  
  try {
    const [wallets, total] = await Promise.all([
      prisma.wallet_address_histories.findMany({
        where,
        skip,
        take,
        orderBy: {
          last_balance_update: 'desc'
        },
        select: {
          id: true,
          address: true,
          coin_type: true,
          network_id: true,
          current_balance: true,
          last_balance_update: true,
          created_at: true,
          user_id: true
        }
      }),
      prisma.wallet_address_histories.count({ where })
    ]);
    
    const totalPages = Math.ceil(total / take);
    
    successResponse(res, 'Wallet balances retrieved successfully', {
      wallets,
      pagination: {
        current_page: parseInt(page as string),
        total_pages: totalPages,
        total_items: total,
        items_per_page: take
      }
    });
    
  } catch (error: any) {
    errorResponse(res, error.message || 'Failed to retrieve wallet balances');
  }
});

/**
 * Get balance statistics
 */
const getBalanceStatistics = catchAsync(async (req, res) => {
  try {
    // Get total balances by network
    const networkStats = await prisma.$queryRaw`
      SELECT 
        n.name as network_name,
        n.coin_type,
        COUNT(w.id) as total_wallets,
        SUM(w.current_balance) as total_balance,
        AVG(w.current_balance) as average_balance,
        MAX(w.last_balance_update) as last_update
      FROM wallet_address_histories w
      JOIN networks n ON w.network_id = n.id
      WHERE w.current_balance > 0
      GROUP BY n.id, n.name, n.coin_type
      ORDER BY total_balance DESC
    `;
    
    // Get recent balance updates
    const recentUpdates = await prisma.wallet_address_histories.findMany({
      where: {
        last_balance_update: {
          not: null
        }
      },
      orderBy: {
        last_balance_update: 'desc'
      },
      take: 10,
      select: {
        address: true,
        coin_type: true,
        current_balance: true,
        last_balance_update: true,
        network_id: true
      }
    });
    
    // Get wallets that haven't been updated recently (older than 1 hour)
    const staleWallets = await prisma.wallet_address_histories.count({
      where: {
        OR: [
          { last_balance_update: null },
          {
            last_balance_update: {
              lt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
            }
          }
        ]
      }
    });
    
    successResponse(res, 'Balance statistics retrieved successfully', {
      network_statistics: networkStats,
      recent_updates: recentUpdates,
      stale_wallets_count: staleWallets
    });
    
  } catch (error: any) {
    errorResponse(res, error.message || 'Failed to retrieve balance statistics');
  }
});

export default {
  updateAllBalances,
  updateNetworkBalances,
  getWalletBalance,
  getAllWalletBalances,
  getBalanceStatistics
};
