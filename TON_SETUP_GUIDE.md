# راهنمای حل مشکل create-system-wallet برای TON

## 🔍 مشکل شناسایی شده

مشکل شما در endpoint `/v1/evm/create-system-wallet` به دلیل عدم وجود شبکه TON در دیتابیس است.

## ✅ راه‌حل

### مرحله 1: اضافه کردن TON به دیتابیس

فایل `add-ton-network.sql` را اجرا کنید:

```bash
# در MySQL/MariaDB
mysql -u your_username -p your_database_name < add-ton-network.sql
```

یا مستقیماً در phpMyAdmin یا هر ابزار مدیریت دیتابیس SQL زیر را اجرا کنید:

```sql
-- 1. اضافه کردن شبکه TON
INSERT INTO networks (name, slug, description, base_type, rpc_url, wss_url, explorer_url, chain_id, status, created_at, updated_at) 
VALUES (
    'TON Mainnet', 
    'ton', 
    'The Open Network (TON) Mainnet', 
    12, 
    'https://toncenter.com/api/v2/jsonRPC', 
    NULL, 
    'https://tonviewer.com', 
    '0', 
    1, 
    NOW(), 
    NOW()
);

-- 2. اضافه کردن TON به شبکه‌های پشتیبانی شده
INSERT INTO supported_networks (type, slug, name, network_type, chain_id, native_currency, gas_limit, status, created_at, updated_at) 
VALUES (
    1, 
    'ton', 
    'TON', 
    1, 
    0, 
    'TON', 
    '1000000', 
    1, 
    NOW(), 
    NOW()
);

-- 3. اضافه کردن کوین TON
INSERT INTO coins (name, coin_type, currency_type, status, admin_approval, network, is_withdrawal, is_deposit, is_buy, is_sell, trade_status, minimum_buy_amount, maximum_buy_amount, minimum_sell_amount, maximum_sell_amount, minimum_withdrawal, maximum_withdrawal, max_send_limit, withdrawal_fees, withdrawal_fees_type, coin_price, decimal, created_at, updated_at) 
VALUES (
    'TON', 
    'TON', 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    0.00000010, 
    999999.00000000, 
    0.00000010, 
    999999.00000000, 
    0.00000010, 
    99999999.00000000, 
    0.00000010, 
    0.000000100000000000, 
    2, 
    1.00000000, 
    9, 
    NOW(), 
    NOW()
);

-- 4. تنظیمات شبکه کوین TON
INSERT INTO coin_networks (uid, network_id, currency_id, type, contract_address, status, withdrawal_fees, withdrawal_fees_type, created_at, updated_at) 
SELECT 
    UUID(), 
    n.id, 
    c.id, 
    2, -- NATIVE_COIN
    '', 
    1, 
    0.000000100000000000, 
    2, 
    NOW(), 
    NOW()
FROM networks n, coins c 
WHERE n.slug = 'ton' AND c.coin_type = 'TON';
```

### مرحله 2: بررسی صحت اضافه شدن

```sql
-- بررسی اینکه TON اضافه شده است
SELECT * FROM networks WHERE slug = 'ton';
SELECT * FROM supported_networks WHERE slug = 'ton';
SELECT * FROM coins WHERE coin_type = 'TON';
```

### مرحله 3: تست API

حالا می‌توانید API را تست کنید:

```bash
curl -X POST http://your-api-url/v1/evm/create-system-wallet \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "network": 12
  }'
```

## 🔧 تغییرات انجام شده

### 1. Validation اضافه شد
- ✅ `systemWalletCreate` validation به `wallet.validation.ts` اضافه شد
- ✅ Route به‌روزرسانی شد تا validation را استفاده کند

### 2. TON Service پیاده‌سازی شد
- ✅ تمام توابع TON پیاده‌سازی شدند
- ✅ پکیج‌های مورد نیاز نصب شدند
- ✅ تست‌ها موفقیت‌آمیز بودند

## 📋 مراحل تست

### 1. تست ایجاد System Wallet
```bash
POST /v1/evm/create-system-wallet
{
  "network": 12
}
```

### 2. تست ایجاد User Wallet
```bash
POST /v1/evm/create-wallet
{
  "coin_type": "TON",
  "network": 12
}
```

## 🚨 مشکلات احتمالی

### 1. خطای "Network not found"
- مطمئن شوید که SQL بالا اجرا شده است
- بررسی کنید که `base_type = 12` در جدول `networks` وجود دارد

### 2. خطای "Invalid base type"
- مطمئن شوید که `TON_BASE_COIN = 12` در `coreConstant.ts` تعریف شده است

### 3. خطای Validation
- مطمئن شوید که `network` در request body ارسال می‌شود
- مقدار `network` باید عدد باشد

## ✅ وضعیت نهایی

پس از اجرای مراحل بالا:

1. ✅ شبکه TON در دیتابیس اضافه می‌شود
2. ✅ Validation برای endpoint اضافه می‌شود
3. ✅ TON service کاملاً پیاده‌سازی شده است
4. ✅ API آماده استفاده است

## 📞 پشتیبانی

اگر همچنان مشکل دارید:

1. لاگ‌های سرور را بررسی کنید
2. مطمئن شوید که دیتابیس به‌روزرسانی شده است
3. مطمئن شوید که سرور restart شده است
4. تست‌های بالا را انجام دهید

---

**وضعیت**: ✅ **آماده برای استفاده**
**تاریخ**: July 2024 