-- Add TON Network to the database
-- Run these SQL commands to add TON support

-- 1. Add TON network to networks table
INSERT INTO networks (name, slug, description, base_type, rpc_url, wss_url, explorer_url, chain_id, status, created_at, updated_at) 
VALUES (
    'TON Mainnet', 
    'ton', 
    'The Open Network (TON) Mainnet', 
    12, 
    'https://toncenter.com/api/v2/jsonRPC', 
    NULL, 
    'https://tonviewer.com', 
    '0', 
    1, 
    NOW(), 
    NOW()
);

-- 2. Add TON to supported networks
INSERT INTO supported_networks (type, slug, name, network_type, chain_id, native_currency, gas_limit, status, created_at, updated_at) 
VALUES (
    1, 
    'ton', 
    'TON', 
    1, 
    0, 
    'TON', 
    '1000000', 
    1, 
    NOW(), 
    NOW()
);

-- 3. Add TON coin to coins table (if not exists)
INSERT INTO coins (name, coin_type, currency_type, status, admin_approval, network, is_withdrawal, is_deposit, is_buy, is_sell, trade_status, minimum_buy_amount, maximum_buy_amount, minimum_sell_amount, maximum_sell_amount, minimum_withdrawal, maximum_withdrawal, max_send_limit, withdrawal_fees, withdrawal_fees_type, coin_price, decimal, created_at, updated_at) 
VALUES (
    'TON', 
    'TON', 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    1, 
    0.00000010, 
    999999.00000000, 
    0.00000010, 
    999999.00000000, 
    0.00000010, 
    99999999.00000000, 
    0.00000010, 
    0.000000100000000000, 
    2, 
    1.00000000, 
    9, 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 4. Add TON coin network configuration
INSERT INTO coin_networks (uid, network_id, currency_id, type, contract_address, status, withdrawal_fees, withdrawal_fees_type, created_at, updated_at) 
SELECT 
    UUID(), 
    n.id, 
    c.id, 
    2, -- NATIVE_COIN
    '', 
    1, 
    0.000000100000000000, 
    2, 
    NOW(), 
    NOW()
FROM networks n, coins c 
WHERE n.slug = 'ton' AND c.coin_type = 'TON'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 5. Add notified_blocks entry for TON
INSERT INTO notified_blocks (network_id, block_number, created_at, updated_at) 
SELECT 
    id, 
    '0', 
    NOW(), 
    NOW()
FROM networks 
WHERE slug = 'ton'
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Verify the entries
SELECT 'Networks' as table_name, COUNT(*) as count FROM networks WHERE slug = 'ton'
UNION ALL
SELECT 'Supported Networks' as table_name, COUNT(*) as count FROM supported_networks WHERE slug = 'ton'
UNION ALL
SELECT 'Coins' as table_name, COUNT(*) as count FROM coins WHERE coin_type = 'TON'
UNION ALL
SELECT 'Coin Networks' as table_name, COUNT(*) as count FROM coin_networks cn 
JOIN networks n ON cn.network_id = n.id 
JOIN coins c ON cn.currency_id = c.id 
WHERE n.slug = 'ton' AND c.coin_type = 'TON'
UNION ALL
SELECT 'Notified Blocks' as table_name, COUNT(*) as count FROM notified_blocks nb 
JOIN networks n ON nb.network_id = n.id 
WHERE n.slug = 'ton'; 