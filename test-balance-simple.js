// Simple test for wallet balance functionality
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSimpleQuery() {
  console.log('Testing simple database query...');
  
  try {
    // Test 1: Get networks
    console.log('1. Getting active networks...');
    const networks = await prisma.networks.findMany({
      where: { status: 1 }
    });
    console.log(`Found ${networks.length} active networks`);
    
    if (networks.length > 0) {
      const network = networks[0];
      console.log(`Testing with network: ${network.name} (ID: ${network.id})`);
      
      // Test 2: Get wallet addresses for this network
      console.log('2. Getting wallet addresses...');
      const networkId = parseInt(network.id.toString());
      console.log(`Converted network ID: ${networkId} (type: ${typeof networkId})`);
      
      const walletAddresses = await prisma.wallet_address_histories.findMany({
        where: {
          network_id: networkId
        },
        take: 5 // Only get first 5 for testing
      });
      
      console.log(`Found ${walletAddresses.length} wallet addresses for network ${network.name}`);
      
      if (walletAddresses.length > 0) {
        console.log('Sample wallet addresses:');
        walletAddresses.forEach((wallet, index) => {
          console.log(`  ${index + 1}. ${wallet.address} (${wallet.coin_type})`);
        });
      }
    }
    
    console.log('✅ Simple test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in simple test:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSimpleQuery();
