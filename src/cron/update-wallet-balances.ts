import { PrismaClient } from '@prisma/client';
import { updateAllWalletBalances } from '../services/balance-update.service';

const prisma = new PrismaClient();

/**
 * Cron job to update wallet balances for all active networks
 * This runs periodically to keep wallet balances up to date
 */
const updateWalletBalances = async () => {
  try {
    console.log("Starting wallet balance update job...");
    
    const startTime = new Date();
    const result = await updateAllWalletBalances();
    const endTime = new Date();
    const duration = endTime.getTime() - startTime.getTime();
    
    if (result.success) {
      console.log(`Wallet balance update completed successfully in ${duration}ms`);
      console.log(`Summary: ${JSON.stringify(result.data, null, 2)}`);
    } else {
      console.error(`Wallet balance update failed: ${result.message}`);
    }
    
  } catch (err: any) {
    console.error("Error in wallet balance update job:", err.stack || err);
  }
};

// Export the function for cron job
export { updateWalletBalances };

// If this file is run directly, execute the function
if (require.main === module) {
  updateWalletBalances()
    .then(() => {
      console.log("Wallet balance update completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Wallet balance update failed:", error);
      process.exit(1);
    });
}
