// Test script for wallet balance update functionality
// Run this with: npm run build && node test-balance-update.js

const { updateAllWalletBalances } = require('./build/src/services/balance-update.service');

async function testBalanceUpdate() {
  console.log('Testing wallet balance update functionality...');
  console.log('Make sure to run: npm run build first\n');

  try {
    const result = await updateAllWalletBalances();

    if (result.success) {
      console.log('✅ Balance update test successful!');
      console.log('Result:', JSON.stringify(result.data, null, 2));
    } else {
      console.log('❌ Balance update test failed:', result.message);
    }
  } catch (error) {
    console.error('❌ Error during balance update test:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testBalanceUpdate();
