import { checkTronDepositsByAddress } from './check-tron-deposits-by-address';
import { updateWalletBalances } from './update-wallet-balances';

/**
 * Initialize all scheduled jobs
 */
export const initCronJobs = () => {
  // Check TRON deposits by address every 1 minute
  // setInterval(async () => {
  //   console.log('Running TRON deposit check by address job...');
  //   await checkTronDepositsByAddress();
  // }, 1 * 60 * 1000); // 1 minute

  // Check EVM and TON deposits every 5 minutes
  // setInterval(async () => {
  //   console.log('Running EVM and TON deposit check job...');
  //   try {
  //     const { checkCoinDeposit } = await import('../services/evm/deposit.service');
  //     await checkCoinDeposit();
  //   } catch (error) {
  //     console.error('Error in deposit check job:', error);
  //   }
  // }, 5 * 60 * 1000); // 5 minutes

  // Update wallet balances every 5 minutes
  setInterval(async () => {
    console.log('Running wallet balance update job...');
    try {
      await updateWalletBalances();
    } catch (error) {
      console.error('Error in wallet balance update job:', error);
    }
  }, 5 * 60 * 1000); // 5 minutes

  console.log('Wallet balance update enabled - runs every 5 minutes');
  // console.log('TRON deposit checking disabled - use endpoint to run manually');
  // console.log('EVM and TON deposit checking disabled - use endpoint /evm/block-deposit-check to run manually');
};
