import { PrismaClient } from '@prisma/client';
import { getTrxBalance } from './evm/trx.tron-web.service';
import { getTrc20TokenBalance } from './evm/trx.token.service';
import { getEthBalance } from './evm/erc20.web3.service';
import { getEthTokenBalance } from './evm/erc20.token.service';
import { TRON_BASE_COIN, EVM_BASE_COIN, SOLANA_BASE_COIN, TON_BASE_COIN } from '../utils/coreConstant';
import { generateSuccessResponse, generateErrorResponse } from '../utils/commonObject';

const prisma = new PrismaClient();

/**
 * Update balance for a specific wallet address
 */
const updateWalletBalance = async (walletAddress: any, network: any) => {
  try {
    let balanceResult: any = null;
    
    // Get balance based on network type
    switch (network.base_type) {
      case TRON_BASE_COIN:
        if (network.is_native) {
          // TRX native balance
          balanceResult = await getTrxBalance(network.rpc_url, walletAddress.address);
        } else {
          // TRC20 token balance
          balanceResult = await getTrc20TokenBalance(
            network.rpc_url, 
            network.contractAddress, 
            walletAddress.address
          );
        }
        break;
        
      case EVM_BASE_COIN:
        if (network.is_native) {
          // ETH/BNB/etc native balance
          balanceResult = await getEthBalance(network.rpc_url, walletAddress.address);
        } else {
          // ERC20 token balance
          balanceResult = await getEthTokenBalance(
            network.rpc_url,
            walletAddress.address,
            network.contractAddress
          );
        }
        break;

      case SOLANA_BASE_COIN:
        // For now, skip Solana - will implement later if needed
        console.log(`Solana balance checking not implemented yet for ${walletAddress.address}`);
        return { success: true, message: 'Solana balance checking skipped' };

      case TON_BASE_COIN:
        // For now, skip TON - will implement later if needed
        console.log(`TON balance checking not implemented yet for ${walletAddress.address}`);
        return { success: true, message: 'TON balance checking skipped' };
        
      default:
        console.log(`Unsupported network type: ${network.base_type}`);
        return { success: false, message: `Unsupported network type: ${network.base_type}` };
    }
    
    if (!balanceResult || !balanceResult.success) {
      console.log(`Failed to get balance for address ${walletAddress.address}: ${balanceResult?.message || 'Unknown error'}`);
      return { success: false, message: balanceResult?.message || 'Failed to get balance' };
    }
    
    const currentBalance = balanceResult.data || 0;
    
    // Update the wallet address balance in database
    await prisma.wallet_address_histories.update({
      where: { id: walletAddress.id },
      data: {
        current_balance: currentBalance,
        last_balance_update: new Date(),
        updated_at: new Date()
      }
    });
    
    console.log(`Updated balance for ${walletAddress.address}: ${currentBalance} ${network.coin_type}`);
    
    return { 
      success: true, 
      data: { 
        address: walletAddress.address, 
        balance: currentBalance, 
        coin_type: network.coin_type 
      } 
    };
    
  } catch (error: any) {
    console.error(`Error updating balance for address ${walletAddress.address}:`, error.stack || error);
    return { success: false, message: error.message || 'Unknown error' };
  }
};

/**
 * Update balances for all wallet addresses in a specific network
 */
const updateNetworkWalletBalances = async (network: any) => {
  try {
    console.log(`Starting balance update for network: ${network.name} (ID: ${network.id})`);
    
    // Get all wallet addresses for this network
    const walletAddresses = await prisma.wallet_address_histories.findMany({
      where: {
        network_id: network.id,
        coin_type: network.coin_type
      }
    });
    
    if (!walletAddresses || walletAddresses.length === 0) {
      console.log(`No wallet addresses found for network ${network.name}`);
      return { success: true, data: [] };
    }
    
    console.log(`Found ${walletAddresses.length} wallet addresses for network ${network.name}`);
    
    const results = [];
    
    // Update balance for each wallet address
    for (const walletAddress of walletAddresses) {
      const result = await updateWalletBalance(walletAddress, network);
      results.push(result);
      
      // Add small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;
    
    console.log(`Balance update completed for network ${network.name}: ${successCount} success, ${failureCount} failures`);
    
    return { 
      success: true, 
      data: {
        network: network.name,
        total: results.length,
        success: successCount,
        failures: failureCount,
        results: results
      }
    };
    
  } catch (error: any) {
    console.error(`Error updating balances for network ${network.name}:`, error.stack || error);
    return { success: false, message: error.message || 'Unknown error' };
  }
};

/**
 * Update balances for all active networks
 */
const updateAllWalletBalances = async () => {
  try {
    console.log("Starting wallet balance update for all networks...");
    
    // Get all active networks
    const networks = await prisma.networks.findMany({
      where: {
        status: 1 // Active networks only
      }
    });
    
    if (!networks || networks.length === 0) {
      console.log("No active networks found");
      return generateSuccessResponse("No active networks found", []);
    }
    
    console.log(`Found ${networks.length} active networks`);
    
    const results = [];
    
    // Process each network
    for (const network of networks) {
      if (!network.rpc_url) {
        console.log(`Network ${network.name} has no RPC URL, skipping`);
        continue;
      }
      
      const result = await updateNetworkWalletBalances(network);
      results.push(result);
      
      // Add delay between networks to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const totalSuccess = results.filter(r => r.success).length;
    const totalFailures = results.filter(r => !r.success).length;
    
    console.log(`Wallet balance update completed: ${totalSuccess} networks processed successfully, ${totalFailures} failures`);
    
    return generateSuccessResponse("Wallet balance update completed", {
      total_networks: results.length,
      successful_networks: totalSuccess,
      failed_networks: totalFailures,
      results: results
    });
    
  } catch (error: any) {
    console.error("Error in wallet balance update:", error.stack || error);
    return generateErrorResponse(error.message || "Failed to update wallet balances");
  }
};

/**
 * Get wallet balance history for a specific address
 */
const getWalletBalanceHistory = async (address: string) => {
  try {
    const walletAddress = await prisma.wallet_address_histories.findFirst({
      where: { address: address }
    });
    
    if (!walletAddress) {
      return generateErrorResponse("Wallet address not found");
    }
    
    return generateSuccessResponse("Wallet balance retrieved successfully", {
      address: walletAddress.address,
      current_balance: walletAddress.current_balance,
      last_balance_update: walletAddress.last_balance_update,
      coin_type: walletAddress.coin_type,
      network_id: walletAddress.network_id
    });
    
  } catch (error: any) {
    console.error("Error getting wallet balance history:", error.stack || error);
    return generateErrorResponse(error.message || "Failed to get wallet balance history");
  }
};

export {
  updateWalletBalance,
  updateNetworkWalletBalances,
  updateAllWalletBalances,
  getWalletBalanceHistory
};
