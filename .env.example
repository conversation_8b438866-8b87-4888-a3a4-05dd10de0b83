# Port number
PORT=3000

# Postgres URL
DATABASE_URL="mysql://root:password@localhost:3306/trademen_test"

# JWT
# JWT secret key
JWT_SECRET=FBDE9C572D46232FF245FE85B12DB

#api secret key
API_SECRET=ukV9dWmvlwOa11TZZscszBzxmVcf5flt

# Number of minutes after which an access token expires
JWT_ACCESS_EXPIRATION_MINUTES=30
# Number of days after which a refresh token expires
JWT_REFRESH_EXPIRATION_DAYS=30
# Number of minutes after which a reset password token expires
JWT_RESET_PASSWORD_EXPIRATION_MINUTES=10
# Number of minutes after which a verify email token expires
JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=10

# SMTP configuration options for the email service
# For testing, you can use a fake SMTP service like Ethereal: https://ethereal.email/create
SMTP_HOST=email-server
SMTP_PORT=587
SMTP_USERNAME=email-server-username
SMTP_PASSWORD=email-server-password
email_from=<EMAIL>

# Tron grid api key
TRONGRID_API_KEY=1bef587d-61af-4d33-933b-cbab4baef07a

ERC_BLOCK_NUMBER = 500
TRC_BLOCK_NUMBER = 20