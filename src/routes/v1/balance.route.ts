import express from "express";
import auth from "../../middlewares/auth";
import balanceController from "../../controllers/balance.controller";

const router = express.Router();

/**
 * @swagger
 * /balance/update-all:
 *   post:
 *     summary: Update balances for all wallet addresses
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Balance update completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post("/update-all", auth(), balanceController.updateAllBalances);

/**
 * @swagger
 * /balance/update-network/{networkId}:
 *   post:
 *     summary: Update balances for a specific network
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: networkId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Network ID
 *     responses:
 *       200:
 *         description: Network balance update completed successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Network not found
 *       500:
 *         description: Internal server error
 */
router.post("/update-network/:networkId", auth(), balanceController.updateNetworkBalances);

/**
 * @swagger
 * /balance/wallet/{address}:
 *   get:
 *     summary: Get balance for a specific wallet address
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: address
 *         required: true
 *         schema:
 *           type: string
 *         description: Wallet address
 *     responses:
 *       200:
 *         description: Wallet balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     address:
 *                       type: string
 *                     current_balance:
 *                       type: string
 *                     last_balance_update:
 *                       type: string
 *                       format: date-time
 *                     coin_type:
 *                       type: string
 *                     network_id:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Wallet address not found
 *       500:
 *         description: Internal server error
 */
router.get("/wallet/:address", auth(), balanceController.getWalletBalance);

/**
 * @swagger
 * /balance/wallets:
 *   get:
 *     summary: Get all wallet balances with pagination
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: network_id
 *         schema:
 *           type: integer
 *         description: Filter by network ID
 *       - in: query
 *         name: coin_type
 *         schema:
 *           type: string
 *         description: Filter by coin type
 *     responses:
 *       200:
 *         description: Wallet balances retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     wallets:
 *                       type: array
 *                       items:
 *                         type: object
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/wallets", auth(), balanceController.getAllWalletBalances);

/**
 * @swagger
 * /balance/statistics:
 *   get:
 *     summary: Get balance statistics and summary
 *     tags: [Balance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Balance statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     network_statistics:
 *                       type: array
 *                       items:
 *                         type: object
 *                     recent_updates:
 *                       type: array
 *                       items:
 *                         type: object
 *                     stale_wallets_count:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get("/statistics", auth(), balanceController.getBalanceStatistics);

export default router;
