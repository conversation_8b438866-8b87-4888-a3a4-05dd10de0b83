import httpStatus from 'http-status';
import catchAsync from '../utils/catchAsync';
import { 
  updateAllWalletBalances, 
  updateNetworkWalletBalances, 
  getWalletBalanceHistory 
} from '../services/balance-update.service';
import { successResponse, errorResponse } from '../utils/common';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Manually trigger balance update for all wallets
 */
const updateAllBalances = catchAsync(async (req, res) => {
  const result = await updateAllWalletBalances();
  
  if (result.success) {
    successResponse(res, result.message, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Update balances for a specific network
 */
const updateNetworkBalances = catchAsync(async (req, res) => {
  const { networkId } = req.params;
  
  if (!networkId) {
    return errorResponse(res, 'Network ID is required');
  }
  
  // Get network details
  const network = await prisma.networks.findUnique({
    where: { id: parseInt(networkId) }
  });
  
  if (!network) {
    return errorResponse(res, 'Network not found');
  }
  
  const result = await updateNetworkWalletBalances(network);
  
  if (result.success) {
    successResponse(res, `Balance update completed for network ${network.name}`, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Get balance for a specific wallet address
 */
const getWalletBalance = catchAsync(async (req, res) => {
  const { address } = req.params;
  
  if (!address) {
    return errorResponse(res, 'Wallet address is required');
  }
  
  const result = await getWalletBalanceHistory(address);
  
  if (result.success) {
    successResponse(res, result.message, result.data);
  } else {
    errorResponse(res, result.message);
  }
});

/**
 * Get all wallet balances with pagination
 */
const getAllWalletBalances = catchAsync(async (req, res) => {
  const { page = 1, limit = 50, network_id, coin_type } = req.query;
  
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);
  const take = parseInt(limit as string);
  
  // Build where clause
  const where: any = {};
  if (network_id) {
    where.network_id = parseInt(network_id as string);
  }
  if (coin_type) {
    where.coin_type = coin_type as string;
  }
  
  try {
    // Use raw SQL to handle potential missing balance columns
    const whereClause = [];
    const params = [];

    if (network_id) {
      whereClause.push('network_id = ?');
      params.push(parseInt(network_id as string));
    }
    if (coin_type) {
      whereClause.push('coin_type = ?');
      params.push(coin_type as string);
    }

    const whereSQL = whereClause.length > 0 ? `WHERE ${whereClause.join(' AND ')}` : '';

    const [wallets, totalResult] = await Promise.all([
      prisma.$queryRawUnsafe(`
        SELECT
          id, address, coin_type, network_id, user_id, created_at,
          COALESCE(current_balance, 0) as current_balance,
          last_balance_update
        FROM wallet_address_histories
        ${whereSQL}
        ORDER BY COALESCE(last_balance_update, created_at) DESC
        LIMIT ${take} OFFSET ${skip}
      `, ...params),
      prisma.$queryRawUnsafe(`
        SELECT COUNT(*) as count
        FROM wallet_address_histories
        ${whereSQL}
      `, ...params)
    ]);

    const total = (totalResult as any)[0]?.count || 0;
    const totalPages = Math.ceil(total / take);

    successResponse(res, 'Wallet balances retrieved successfully', {
      wallets,
      pagination: {
        current_page: parseInt(page as string),
        total_pages: totalPages,
        total_items: total,
        items_per_page: take
      }
    });
    
  } catch (error: any) {
    errorResponse(res, error.message || 'Failed to retrieve wallet balances');
  }
});

/**
 * Get balance statistics
 */
const getBalanceStatistics = catchAsync(async (req, res) => {
  try {
    // Get total balances by network using raw SQL
    const networkStats = await prisma.$queryRaw`
      SELECT
        n.name as network_name,
        n.coin_type,
        COUNT(w.id) as total_wallets,
        COALESCE(SUM(w.current_balance), 0) as total_balance,
        COALESCE(AVG(w.current_balance), 0) as average_balance,
        MAX(w.last_balance_update) as last_update
      FROM wallet_address_histories w
      JOIN networks n ON w.network_id = n.id
      WHERE COALESCE(w.current_balance, 0) > 0
      GROUP BY n.id, n.name, n.coin_type
      ORDER BY total_balance DESC
    `;

    // Get recent balance updates using raw SQL
    const recentUpdates = await prisma.$queryRaw`
      SELECT
        address, coin_type, network_id,
        COALESCE(current_balance, 0) as current_balance,
        last_balance_update
      FROM wallet_address_histories
      WHERE last_balance_update IS NOT NULL
      ORDER BY last_balance_update DESC
      LIMIT 10
    `;

    // Get wallets that haven't been updated recently using raw SQL
    const staleWalletsResult = await prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM wallet_address_histories
      WHERE last_balance_update IS NULL
         OR last_balance_update < DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `;

    const staleWallets = (staleWalletsResult as any)[0]?.count || 0;

    successResponse(res, 'Balance statistics retrieved successfully', {
      network_statistics: networkStats,
      recent_updates: recentUpdates,
      stale_wallets_count: staleWallets
    });

  } catch (error: any) {
    console.error('Error in getBalanceStatistics:', error);

    // Fallback to basic statistics without balance columns
    try {
      const basicStats = await prisma.$queryRaw`
        SELECT
          n.name as network_name,
          n.coin_type,
          COUNT(w.id) as total_wallets
        FROM wallet_address_histories w
        JOIN networks n ON w.network_id = n.id
        GROUP BY n.id, n.name, n.coin_type
        ORDER BY total_wallets DESC
      `;

      successResponse(res, 'Basic statistics retrieved successfully (balance columns not available)', {
        network_statistics: basicStats,
        recent_updates: [],
        stale_wallets_count: 0,
        note: 'Balance tracking columns not yet added to database'
      });

    } catch (fallbackError: any) {
      errorResponse(res, fallbackError.message || 'Failed to retrieve balance statistics');
    }
  }
});

export default {
  updateAllBalances,
  updateNetworkBalances,
  getWalletBalance,
  getAllWalletBalances,
  getBalanceStatistics
};
