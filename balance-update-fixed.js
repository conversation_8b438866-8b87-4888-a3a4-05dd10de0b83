"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWalletBalanceHistory = exports.updateAllWalletBalances = exports.updateNetworkWalletBalances = exports.updateWalletBalance = void 0;
const client_1 = require("@prisma/client");
const trx_tron_web_service_1 = require("./evm/trx.tron-web.service");
const trx_token_service_1 = require("./evm/trx.token.service");
const erc20_web3_service_1 = require("./evm/erc20.web3.service");
const erc20_token_service_1 = require("./evm/erc20.token.service");
const coreConstant_1 = require("../utils/coreConstant");
const commonObject_1 = require("../utils/commonObject");
const prisma = new client_1.PrismaClient();

const updateWalletBalance = async (walletAddress, network) => {
    try {
        let balanceResult = null;
        switch (network.base_type) {
            case coreConstant_1.TRON_BASE_COIN:
                if (network.is_native) {
                    balanceResult = await (0, trx_tron_web_service_1.getTrxBalance)(network.rpc_url, walletAddress.address);
                }
                else {
                    balanceResult = await (0, trx_token_service_1.getTrc20TokenBalance)(network.rpc_url, network.contractAddress, walletAddress.address);
                }
                break;
            case coreConstant_1.EVM_BASE_COIN:
                if (network.is_native) {
                    balanceResult = await (0, erc20_web3_service_1.getEthBalance)(network.rpc_url, walletAddress.address);
                }
                else {
                    balanceResult = await (0, erc20_token_service_1.getEthTokenBalance)(network.rpc_url, walletAddress.address, network.contractAddress);
                }
                break;
            case coreConstant_1.SOLANA_BASE_COIN:
                console.log(`Solana balance checking not implemented yet for ${walletAddress.address}`);
                return { success: true, message: 'Solana balance checking skipped' };
            case coreConstant_1.TON_BASE_COIN:
                console.log(`TON balance checking not implemented yet for ${walletAddress.address}`);
                return { success: true, message: 'TON balance checking skipped' };
            default:
                console.log(`Unsupported network type: ${network.base_type}`);
                return { success: false, message: `Unsupported network type: ${network.base_type}` };
        }
        if (!balanceResult || !balanceResult.success) {
            console.log(`Failed to get balance for address ${walletAddress.address}: ${(balanceResult === null || balanceResult === void 0 ? void 0 : balanceResult.message) || 'Unknown error'}`);
            return { success: false, message: (balanceResult === null || balanceResult === void 0 ? void 0 : balanceResult.message) || 'Failed to get balance' };
        }
        const currentBalance = balanceResult.data || 0;
        
        // Try to update with balance columns using raw SQL
        try {
            await prisma.$executeRaw`
                UPDATE wallet_address_histories 
                SET current_balance = ${currentBalance}, 
                    last_balance_update = NOW(), 
                    updated_at = NOW() 
                WHERE id = ${walletAddress.id}
            `;
        } catch (error) {
            // If columns don't exist yet, log the balance info and update only existing columns
            console.log(`Balance info for ${walletAddress.address}: ${currentBalance} ${network.coin_type} (balance columns not yet added to DB)`);
            
            // Update only the updated_at field
            await prisma.wallet_address_histories.update({
                where: { id: walletAddress.id },
                data: {
                    updated_at: new Date()
                }
            });
        }
        
        console.log(`Updated balance for ${walletAddress.address}: ${currentBalance} ${network.coin_type}`);
        return {
            success: true,
            data: {
                address: walletAddress.address,
                balance: currentBalance,
                coin_type: network.coin_type
            }
        };
    }
    catch (error) {
        console.error(`Error updating balance for address ${walletAddress.address}:`, error.stack || error);
        return { success: false, message: error.message || 'Unknown error' };
    }
};
exports.updateWalletBalance = updateWalletBalance;

const updateNetworkWalletBalances = async (network) => {
    try {
        console.log(`Starting balance update for network: ${network.name} (ID: ${network.id})`);
        console.log(`Network details:`, {
            id: network.id,
            name: network.name,
            base_type: network.base_type,
            coin_type: network.coin_type,
            is_native: network.is_native,
            rpc_url: network.rpc_url ? 'present' : 'missing'
        });

        // Ensure network ID is properly converted to integer
        const networkId = typeof network.id === 'string' ? parseInt(network.id) : Number(network.id);
        console.log(`Converted network ID: ${networkId} (type: ${typeof networkId})`);

        let walletAddresses;
        try {
            walletAddresses = await prisma.wallet_address_histories.findMany({
                where: {
                    network_id: networkId
                }
            });
        } catch (queryError) {
            console.error(`Error querying wallet addresses for network ${network.name}:`, queryError.message);
            return { success: false, message: `Database query failed: ${queryError.message}` };
        }
        
        if (!walletAddresses || walletAddresses.length === 0) {
            console.log(`No wallet addresses found for network ${network.name}`);
            return { success: true, data: [] };
        }
        console.log(`Found ${walletAddresses.length} wallet addresses for network ${network.name}`);
        const results = [];
        for (const walletAddress of walletAddresses) {
            const result = await updateWalletBalance(walletAddress, network);
            results.push(result);
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        console.log(`Balance update completed for network ${network.name}: ${successCount} success, ${failureCount} failures`);
        return {
            success: true,
            data: {
                network: network.name,
                total: results.length,
                success: successCount,
                failures: failureCount,
                results: results
            }
        };
    }
    catch (error) {
        console.error(`Error updating balances for network ${network.name}:`, error.stack || error);
        return { success: false, message: error.message || 'Unknown error' };
    }
};
exports.updateNetworkWalletBalances = updateNetworkWalletBalances;

const updateAllWalletBalances = async () => {
    try {
        console.log("Starting wallet balance update for all networks...");
        const networks = await prisma.networks.findMany({
            where: {
                status: 1
            }
        });
        if (!networks || networks.length === 0) {
            console.log("No active networks found");
            return (0, commonObject_1.generateSuccessResponse)("No active networks found", []);
        }
        console.log(`Found ${networks.length} active networks`);
        const results = [];
        for (const network of networks) {
            if (!network.rpc_url) {
                console.log(`Network ${network.name} has no RPC URL, skipping`);
                continue;
            }
            const result = await updateNetworkWalletBalances(network);
            results.push(result);
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        const totalSuccess = results.filter(r => r.success).length;
        const totalFailures = results.filter(r => !r.success).length;
        console.log(`Wallet balance update completed: ${totalSuccess} networks processed successfully, ${totalFailures} failures`);
        return (0, commonObject_1.generateSuccessResponse)("Wallet balance update completed", {
            total_networks: results.length,
            successful_networks: totalSuccess,
            failed_networks: totalFailures,
            results: results
        });
    } 
    catch (error) {
        console.error("Error in wallet balance update:", error.stack || error);
        return (0, commonObject_1.generateErrorResponse)(error.message || "Failed to update wallet balances");
    }
};
exports.updateAllWalletBalances = updateAllWalletBalances;

const getWalletBalanceHistory = async (address) => {
    try {
        // Try to get wallet with balance columns using raw SQL
        const walletData = await prisma.$queryRaw`
            SELECT 
                id, address, coin_type, network_id, user_id,
                COALESCE(current_balance, 0) as current_balance,
                last_balance_update,
                created_at, updated_at
            FROM wallet_address_histories 
            WHERE address = ${address}
            LIMIT 1
        `;
        
        if (!walletData || walletData.length === 0) {
            return (0, commonObject_1.generateErrorResponse)("Wallet address not found");
        }
        
        const wallet = walletData[0];
        
        return (0, commonObject_1.generateSuccessResponse)("Wallet balance retrieved successfully", {
            address: wallet.address,
            current_balance: wallet.current_balance || 0,
            last_balance_update: wallet.last_balance_update,
            coin_type: wallet.coin_type,
            network_id: wallet.network_id,
            user_id: wallet.user_id
        });
        
    } catch (error) {
        console.error("Error getting wallet balance history:", error.stack || error);
        
        // Fallback to basic query without balance columns
        try {
            const walletAddress = await prisma.wallet_address_histories.findFirst({
                where: { address: address }
            });
            
            if (!walletAddress) {
                return (0, commonObject_1.generateErrorResponse)("Wallet address not found");
            }
            
            return (0, commonObject_1.generateSuccessResponse)("Wallet balance retrieved successfully (basic info)", {
                address: walletAddress.address,
                current_balance: 0, // Default since columns don't exist yet
                last_balance_update: null,
                coin_type: walletAddress.coin_type,
                network_id: walletAddress.network_id,
                user_id: walletAddress.user_id
            });
            
        } catch (fallbackError) {
            return (0, commonObject_1.generateErrorResponse)(fallbackError.message || "Failed to get wallet balance history");
        }
    }
};
exports.getWalletBalanceHistory = getWalletBalanceHistory;
