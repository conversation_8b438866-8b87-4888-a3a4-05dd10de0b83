import { PrismaClient } from "@prisma/client";
import { custome_decrypt, createUniqueCode, custom<PERSON>rom<PERSON>ei } from "../../utils/helper";
import { TON_BASE_COIN, STATUS_ACTIVE, STATUS_PENDING, SOLANA_BASE_COIN, ADDRESS_TYPE_EXTERNAL } from "../../utils/coreConstant";
import { generateErrorResponse, generateSuccessResponse } from "../../utils/commonObject";
import { TonClient, WalletContractV4, internal, Address, toNano, fromNano, beginCell, Cell } from 'ton';
import { mnemonicToWalletKey, mnemonicNew, keyPairFromSecretKey } from 'ton-crypto';

const prisma = new PrismaClient();

// Initialize TON client
const initializeTonClient = (rpcUrl: string) => {
    try {
        // Add API key for toncenter.com
        const apiKey = 'bfaec8c9a15446ce9323908e5a3e6817b2dc08b49c1a42acb1bc19b58e547daa';
        
        return new TonClient({
            endpoint: rpcUrl,
            apiKey: apiKey, // Add API key to avoid rate limiting
            timeout: 15000, // Increase timeout
        });
    } catch (error) {
        throw new Error(`Failed to initialize TON client: ${error.message}`);
    }
};

// Retry function for TON API calls
const retryTonApiCall = async (apiCall: () => Promise<any>, maxRetries: number = 3) => {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await apiCall();
        } catch (error: any) {
            if (error.response?.status === 429 && i < maxRetries - 1) {
                console.log(`Rate limit hit, retrying in ${(i + 1) * 2} seconds...`);
                await new Promise(resolve => setTimeout(resolve, (i + 1) * 2000));
                continue;
            }
            throw error;
        }
    }
};

// Generate mnemonic for TON wallet
const generateMnemonic = async () => {
    try {
        return await mnemonicNew();
    } catch (error) {
        throw new Error(`Failed to generate mnemonic: ${error.message}`);
    }
};

// Create TON wallet
const createTonAddress = async (rpcUrl?: string) => {
    try {
        const client = await initializeTonClient(rpcUrl || 'https://toncenter.com/api/v2/jsonRPC');
        
        // Generate mnemonic (12 words)
        const mnemonic = await mnemonicNew();
        const key = await mnemonicToWalletKey(mnemonic);
        
        // Create wallet
        const wallet = WalletContractV4.create({ 
            publicKey: key.publicKey, 
            workchain: 0 
        });
        
        const data = {
            address: wallet.address.toString(),
            pk: mnemonic.join(' '), // Store mnemonic instead of hex private key
        };
        
        return generateSuccessResponse("TON wallet created successfully", data);
    } catch(err: any) {
        console.log("createTonAddress service", err);
        return generateErrorResponse(err.message || "Something went wrong");
    }
};

// Send TON coins
const sendTonCoin = async (
    rpcUrl: string,
    toAddress: string,
    amount: number,
    privateKey: string
) => {
    try {
        const client = initializeTonClient(rpcUrl);
        
        // Parse addresses
        let fromAddress: Address;
        let toAddr: Address;
        try {
            toAddr = Address.parse(toAddress);
        } catch (error) {
            return generateErrorResponse("Invalid to address format");
        }
        
        // Parse private key (could be mnemonic or hex)
        let key;
        if (privateKey.trim().split(' ').length > 1) {
            // mnemonic
            const mnemonic = privateKey.split(' ');
            key = await mnemonicToWalletKey(mnemonic);
        } else {
            // hex private key
            const secretKey = Buffer.from(privateKey, 'hex');
            const { publicKey } = keyPairFromSecretKey(secretKey);
            key = { secretKey, publicKey };
        }
        
        // Create wallet instance
        const wallet = WalletContractV4.create({ 
            publicKey: key.publicKey, 
            workchain: 0 
        });
        const contract = client.open(wallet);
        
        // Get wallet address
        fromAddress = wallet.address;
        
        // Get balance with retry logic
        const balance = await retryTonApiCall(async () => {
            return await client.getBalance(fromAddress);
        });
        const balanceInTon = parseFloat(fromNano(balance));
        
        if (balanceInTon < amount) {
            return generateErrorResponse(`Insufficient balance. Available: ${balanceInTon} TON, Required: ${amount} TON`);
        }
        
        // Calculate amount in nano
        const amountInNano = toNano(amount.toString());
        
        // Create transfer message
        const transfer = contract.createTransfer({
            secretKey: key.secretKey,
            messages: [
                internal({
                    to: toAddr,
                    value: amountInNano,
                    bounce: false,
                })
            ],
            seqno: await contract.getSeqno(),
        });
        
        // Send transaction with retry logic
        await retryTonApiCall(async () => {
            return await contract.send(transfer);
        });
        
        // اینجا باید وضعیت تراکنش را چک کنید
        // فرض کنید txHash یا اطلاعات تراکنش را دارید (در TON باید با lt و hash یا getTransactions بررسی کنید)
        const txs = await retryTonApiCall(async () => {
            return await client.getTransactions(fromAddress, { limit: 5 });
        });
        
        // پیدا کردن آخرین تراکنش outgoing
        const lastTx = txs.find(tx => tx.outMessagesCount > 0);
        
        if (lastTx && lastTx.description && lastTx.description.aborted) {
            // تراکنش شکست خورده
            return generateErrorResponse('Transaction failed: ' + (lastTx.description.reason || 'aborted by network'));
        }
        
        // Generate transaction hash (TON doesn't return immediate hash)
        const txHash = `ton_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        console.log('TON transaction sent:', {
            from: fromAddress.toString(),
            to: toAddr.toString(),
            amount: amount,
            txHash
        });
        
        return generateSuccessResponse("TON sent successfully", {
            transaction_id: txHash,
            used_gas: 0
        });
    } catch (err: any) {
        console.log('TON send error:', err); // Added for detailed error debugging
        let errorMsg = err?.response?.data?.error || err?.message || 'Something went wrong';
        return generateErrorResponse('TON transaction failed: ' + errorMsg);
    }
};

// Send TON tokens (Jetton)
const sendTonToken = async (
    rpcUrl: string,
    tokenAddress: string,
    toAddress: string,
    decimal: number,
    amount: number,
    privateKey: string
) => {
    try {
        const client = initializeTonClient(rpcUrl);
        let key;
        if (privateKey.trim().split(' ').length > 1) {
            // mnemonic
            const mnemonic = privateKey.split(' ');
            key = await mnemonicToWalletKey(mnemonic);
        } else {
            // hex private key
            const secretKey = Buffer.from(privateKey, 'hex');
            const { publicKey } = keyPairFromSecretKey(secretKey);
            key = { secretKey, publicKey };
        }
        // Create wallet instance
        const wallet = WalletContractV4.create({ publicKey: key.publicKey, workchain: 0 });
        const contract = client.open(wallet);
        // Validate addresses
        let recipient: Address;
        let jettonContract: Address;
        try {
            recipient = Address.parse(toAddress);
            jettonContract = Address.parse(tokenAddress);
        } catch (error) {
            return generateErrorResponse("Invalid address format");
        }
        // Calculate token amount with decimals
        const tokenAmount = amount * Math.pow(10, decimal);
        // Create Jetton transfer message
        const transferBody = beginCell()
            .storeUint(0xf8a7ea5, 32) // transfer op
            .storeUint(0, 64) // query id
            .storeAddress(recipient) // destination
            .storeAddress(contract.address) // response destination
            .storeCoins(toNano('0.05')) // forward amount
            .storeUint(0, 1) // forward payload in this slice
            .storeCoins(tokenAmount) // amount
            .endCell();
        // Create transfer message
        const transfer = contract.createTransfer({
            secretKey: key.secretKey,
            messages: [
                internal({
                    to: jettonContract,
                    value: toNano('0.1'),
                    bounce: true,
                    body: transferBody,
                })
            ],
            seqno: await contract.getSeqno(),
        });
        // Send transaction
        await contract.send(transfer);
        // Generate a transaction hash
        const txHash = `ton_jetton_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return generateSuccessResponse("TON Token transaction successful", {
            transaction_id: txHash,
            used_gas: 0
        });
    } catch (error) {
        console.log("sendTonToken service", error);
        return generateErrorResponse(error.message || "Something went wrong");
    }
}

// Get latest block number
const getLatestBlockNumber = async (rpc: string) => {
    try {
        const client = initializeTonClient(rpc);
        // For now, return a placeholder since TON API is complex
        // In production, you'd implement proper block number retrieval
        return Math.floor(Date.now() / 1000); // Placeholder
    } catch (error) {
        console.log("getLatestBlockNumber service", error);
        return 0;
    }
}

// Get TON transaction by hash
const getTonTransactionByTrx = async (rpc: string, transaction_hash: string) => {
    try {
        // For now, return a placeholder response since TON transaction parsing is complex
        // In production, you'd implement proper transaction parsing
        
        return generateSuccessResponse("Transaction found successfully", {
            tx_type: 'native',
            from_address: '',
            to_address: '',
            amount: '0',
            block_number: '0',
            transaction_id: transaction_hash,
            contract_address: '',
            fee_limit: '0',
        });
    } catch (error) {
        console.log("getTonTransactionByTrx service", error);
        return generateErrorResponse(error.message || "Something went wrong");
    }
}

// Get TON contract details (Jetton)
const getTonContractDetails = async (rpc: string, tokenHolder: string, tokenAddress: string) => {
    try {
        const client = initializeTonClient(rpc);
        
        // Parse addresses
        let holder: Address;
        let contract: Address;
        try {
            holder = Address.parse(tokenHolder);
            contract = Address.parse(tokenAddress);
        } catch (error) {
            return generateErrorResponse("Invalid address format");
        }
        
        // Get contract data
        const contractData = await client.getContractState(contract);
        
        if (contractData.state !== 'active') {
            return generateErrorResponse("Contract not found or inactive");
        }
        
        // Parse metadata (simplified - in production you'd parse the actual metadata)
        const contractInfo = {
            name: "Jetton Token",
            symbol: "JETTON",
            decimals: 9,
            totalSupply: "0",
            owner: holder.toString(),
        };
        
        return generateSuccessResponse("Contract details retrieved", contractInfo);
    } catch (error) {
        console.log("getTonContractDetails service", error);
        return generateErrorResponse("Something went wrong");
    }
}

// Take coins from TON network (for deposit processing)
const takeCoinFromTonNetwork = async (network: any, systemWallet: any, userWallet: any): Promise<any> => {
    try {
        const client = initializeTonClient(network.rpc_url);
        
        // Parse addresses
        let userAddress: Address;
        let systemAddress: Address;
        try {
            userAddress = Address.parse(userWallet.address);
            systemAddress = Address.parse(systemWallet.address);
        } catch (error) {
            return generateErrorResponse("Invalid address format");
        }
        
        // Get user wallet balance
        const userBalance = await client.getBalance(userAddress);
        const userBalanceInTon = parseFloat(fromNano(userBalance));
        
        console.log('User TON balance:', userBalanceInTon);
        console.log('Required amount:', network.amount);
        
        if (userBalanceInTon < network.amount) {
            return generateErrorResponse(`Insufficient balance. User has ${userBalanceInTon} TON, required ${network.amount} TON`);
        }
        
        // Get **user** wallet private key (we need to sign from the wallet that actually has the funds)
        const userPrivateKey = await custome_decrypt(userWallet.wallet_key);
        let userKey;

        if (userPrivateKey.trim().split(' ').length > 1) {
            // mnemonic
            const userMnemonic = userPrivateKey.split(' ');
            userKey = await mnemonicToWalletKey(userMnemonic);
        } else {
            // hex private key
            const secretKey = Buffer.from(userPrivateKey, 'hex');
            const { publicKey } = keyPairFromSecretKey(secretKey);
            userKey = { secretKey, publicKey };
        }

        // Create user wallet instance (sender)
        const userWalletContract = WalletContractV4.create({
            publicKey: userKey.publicKey,
            workchain: 0,
        });

        // Log derived vs expected for debugging
        console.log('Derived address from key:', userWalletContract.address.toString());
        console.log('Expected user address:', userWallet.address);

        const userContract = client.open(userWalletContract);
        
        // Calculate amount in nano (no fee deduction)
        let sendAmount = Number(network.amount);
        const roundedAmount = sendAmount.toFixed(9);
        const amountInNano = toNano(roundedAmount);
        
        // Create transfer message *from user wallet to system wallet*
        const transfer = userContract.createTransfer({
            secretKey: userKey.secretKey,
            messages: [
                internal({
                    to: systemAddress,
                    value: amountInNano,
                    bounce: false,
                }),
            ],
            seqno: await userContract.getSeqno(),
        });

        // Send transaction
        await userContract.send(transfer);
        
        // Generate transaction hash
        const txHash = `ton_deposit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Update database
        const transaction = await prisma.deposite_transactions.update({
            where: { id: network.transaction_id },
            data: { status: STATUS_ACTIVE, is_admin_receive: STATUS_ACTIVE }
        });
        
        const adminTokenReceive = await prisma.admin_receive_token_transaction_histories.create({
            data: {
                unique_code: createUniqueCode(),
                amount: network.amount,
                deposit_id: Number(network.transaction_id),
                fees: "0",
                to_address: systemWallet.address,
                from_address: userWallet.address,
                transaction_hash: txHash,
                status: STATUS_ACTIVE,
                type: 1, // 1 = deposit type
            }
        });
        
        return generateSuccessResponse("Coins received successfully");
    } catch(err: any) {
        console.log(err);
        return generateErrorResponse(err.stack);
    }
}

// Get TON address by private key or mnemonic
const getTonAddressByKey = async(pk: string) => {
    try {
        let key;
        if (pk.trim().split(' ').length > 1) {
            // mnemonic
            const mnemonic = pk.split(' ');
            key = await mnemonicToWalletKey(mnemonic);
        } else {
            // hex private key
            const secretKey = Buffer.from(pk, 'hex');
            const { publicKey } = keyPairFromSecretKey(secretKey);
            key = { secretKey, publicKey };
        }
        const wallet = WalletContractV4.create({ publicKey: key.publicKey, workchain: 0 });
        return {
            address: wallet.address.toString()
        };
    } catch(err: any) {
        console.log('getTonAddressByKey ex', err.stack);
        return {};
    }
}

// Estimate TON transaction fee
const getEstimateFee = async (rpcUrl: string, fromAddress: string, toAddress: string) => {
    try {
        // TON fees are relatively fixed, but we can estimate based on current network conditions
        const estimatedFee = toNano('0.01'); // ~0.01 TON for basic transfer
        
        return generateSuccessResponse("Estimate fees fetch successfully", estimatedFee.toString());
    } catch (error) {
        console.log("TON getEstimateFee ", error);
        return generateErrorResponse("Estimate fee failed to fetch", 0);
    }
}

// Search blocks for transactions
const searchBlockByBlockTon = async (rpc: string, fromBlockNumber: number, toBlockNumber: number) => {
    try {
        // For now, return a placeholder since TON block scanning is complex
        // In production, you'd implement proper block scanning
        
        const transactions = [];
        
        // Placeholder transaction data
        const txData = {
            transaction_id: `ton_tx_${Date.now()}`,
            block_number: fromBlockNumber,
            from_address: '',
            to_address: '',
            amount: '0',
            fee_limit: '0',
        };
        
        transactions.push(txData);
        
        if (transactions.length > 0) {
            return generateSuccessResponse("Scanned transaction found successfully", {
                transactions: transactions,
                blockData: {
                    from_block_number: fromBlockNumber,
                    to_block_number: toBlockNumber
                }
            });
        }
        
        return generateErrorResponse("Scanned transaction not found");
    } catch (error) {
        console.log("searchBlockByBlockTon service", error);
        return generateErrorResponse("Something went wrong");
    }
}

// Get TON transactions for a specific block
const getTonTransactions = async (rpcUrl: string, blockNumber: number) => {
    try {
        // For TON, we'll use address scanning instead of block scanning
        // This is more efficient for TON blockchain
        return generateSuccessResponse("Address scanning enabled", {
            data: [],
            block_number: blockNumber
        });
    } catch(err: any) {
        console.log("getTonTransactions service", err);
        return generateErrorResponse(err.message || "Something went wrong");
    }
};

// Check TON deposit address
const checkTonDepositAddress = async (rpcUrl: string, transaction: any, blockNumber: number) => {
    try {
        // This function will be called for address scanning
        // For now, return null as we'll implement address-based scanning
        return null;
    } catch(err: any) {
        console.log("checkTonDepositAddress service", err);
        return null;
    }
};

// Check TON deposits by scanning wallet addresses (more efficient for TON)
const checkTonDepositsByAddresses = async (rpcUrl: string, networkId: number) => {
    try {
        let resultData: any = [];

        // Get all wallet addresses for this network
        const walletAddresses = await prisma.wallet_address_histories.findMany({
            where: {
                network_id: Number(networkId)
            }
        });

        if (!walletAddresses || walletAddresses.length === 0) {
            console.log('No wallet addresses found for TON network ID:', networkId);
            return generateSuccessResponse("No wallet addresses found", []);
        }

        console.log(`Found ${walletAddresses.length} TON wallet addresses for network ID: ${networkId}`);

        const client = initializeTonClient(rpcUrl);

        // Check each wallet address for new transactions
        for (const walletAddress of walletAddresses) {
            try {
                const address = Address.parse(walletAddress.address);
                const currentBalance = await retryTonApiCall(async () => {
                    return await client.getBalance(address);
                });
                const currentBalanceInTon = parseFloat(fromNano(currentBalance));
                
                console.log(`TON wallet ${walletAddress.address} current balance: ${currentBalanceInTon}`);
                
                // Get the last known balance from database or use 0 as default
                let lastKnownBalance = 0;
                
                // Check if there's a recent deposit transaction for this address
                const lastDeposit = await prisma.deposite_transactions.findFirst({
                    where: {
                        address: walletAddress.address,
                        coin_id: Number(walletAddress.coin_id)
                    },
                    orderBy: {
                        created_at: 'desc'
                    }
                });
                
                if (lastDeposit) {
                    // Calculate expected balance based on last deposit
                    lastKnownBalance = parseFloat(lastDeposit.amount.toString()) || 0;
                }
                
                // If current balance is higher than last known balance, there's a new deposit
                if (currentBalanceInTon > lastKnownBalance) {
                    const depositAmount = currentBalanceInTon - lastKnownBalance;
                    
                    console.log(`New TON deposit detected: ${depositAmount} TON to ${walletAddress.address}`);
                    
                    // Try to get recent transactions to find sender address and real tx hash
                    let senderAddress = 'TON_Network';
                    let foundTx = null;
                    let txHash = null;
                    try {
                        console.log(`Getting recent transactions for ${walletAddress.address}...`);
                        
                        // Get recent transactions for this address with retry logic
                        const transactions = await retryTonApiCall(async () => {
                            return await client.getTransactions(address, {
                                limit: 10
                            });
                        });
                        
                        if (transactions && transactions.length > 0) {
                            // Look for incoming transactions with sender information and matching amount
                            for (let i = 0; i < transactions.length; i++) {
                                const tx = transactions[i];
                                let txAmount = 0;
                                if (
                                    tx.inMessage &&
                                    tx.inMessage.info &&
                                    tx.inMessage.info.value &&
                                    typeof tx.inMessage.info.value.coins !== 'undefined'
                                ) {
                                    txAmount = parseFloat(fromNano(tx.inMessage.info.value.coins));
                                }
                                if (Math.abs(txAmount - depositAmount) < 0.000001) {
                                    foundTx = tx;
                                    if (tx.inMessage && tx.inMessage.info && tx.inMessage.info.src) {
                                        try {
                                            const parsedAddress = Address.parse(tx.inMessage.info.src.toString());
                                            senderAddress = parsedAddress.toString({ urlSafe: true });
                                        } catch (error) {
                                            senderAddress = tx.inMessage.info.src.toString();
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                        if (!foundTx) {
                            senderAddress = 'TON_External_Transfer';
                        } else {
                            if (foundTx.hash) {
                                if (typeof foundTx.hash === 'function') {
                                    try {
                                        txHash = foundTx.hash().toString('hex');
                                    } catch (e) {
                                        try {
                                            txHash = foundTx.hash().toString();
                                        } catch (ee) {
                                            txHash = null;
                                        }
                                    }
                                } else {
                                    txHash = foundTx.hash.toString();
                                }
                            }
                        }
                    } catch (txError) {
                        senderAddress = 'TON_External_Sender';
                    }
                    // Create deposit data
                    const depositData = {
                        address: walletAddress.address,
                        receiver_wallet_id: Number(walletAddress.wallet_id),
                        address_type: ADDRESS_TYPE_EXTERNAL,
                        coin_type: walletAddress.coin_type,
                        amount: depositAmount,
                        transaction_id: txHash ? txHash : `ton_deposit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                        status: 1,
                        confirmations: 1,
                        from_address: senderAddress,
                        network_type: Number(walletAddress.network_id),
                        network_id: Number(walletAddress.network_id),
                        block_number: Date.now(), // Use timestamp as block number for TON
                        coin_id: Number(walletAddress.coin_id),
                    };
                    
                    const alreadyExists = await prisma.deposite_transactions.findFirst({
                        where: { transaction_id: txHash }
                    });
                    if (!alreadyExists) {
                        // فقط اگر قبلاً ثبت نشده، اضافه کن
                        resultData.push(depositData);
                        // ... ثبت در دیتابیس ...
                    }
                }
                
            } catch (error) {
                console.log(`Error checking TON wallet ${walletAddress.address}:`, error);
            }
        }

        console.log(`TON deposit check completed. Found ${resultData.length} new deposits.`);
        return generateSuccessResponse("TON address scanning completed", resultData);
    } catch(err: any) {
        console.log("checkTonDepositsByAddresses service", err);
        return generateErrorResponse(err.message || "Something went wrong");
    }
};

export {
    createTonAddress,
    sendTonCoin,
    sendTonToken,
    getLatestBlockNumber,
    getTonContractDetails,
    getTonTransactionByTrx,
    takeCoinFromTonNetwork,
    getTonAddressByKey,
    getEstimateFee,
    searchBlockByBlockTon,
    getTonTransactions,
    checkTonDepositAddress,
    checkTonDepositsByAddresses
} 