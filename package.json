{"name": "prisma-express-typescript-boilerplate", "version": "1.0.0", "description": "REST API Boilerplate with Node JS, TypeScript, Express and Prisma", "main": "src/index.ts", "repository": "https://github.com/antonio-lazaro/prisma-express-typescript-boilerplate.git", "scripts": {"start": "yarn build && NODE_ENV=production node build/src/index.js", "dev": "cross-env NODE_ENV=development nodemon src/index.ts", "test": "docker-compose -f docker-compose.only-db-test.yml up -d && yarn db:push && jest -i --colors --verbose --detectOpenHandles && docker-compose -f docker-compose.only-db-test.yml down", "test:watch": "docker-compose -f docker-compose.only-db-test.yml up -d && yarn db:push && jest -i --watchAll && docker-compose -f docker-compose.only-db-test.yml down", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check **/*.ts", "prettier:fix": "prettier --write **/*.ts", "db:push": "prisma db push", "db:studio": "prisma studio", "docker:prod": "docker-compose -f docker-compose.yml -f docker-compose.prod.yml up", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.test.yml up", "docker:dev-db:start": "docker-compose -f docker-compose.only-db-dev.yml up -d", "docker:dev-db:stop": "docker-compose -f docker-compose.only-db-dev.yml down", "prepare": "husky install", "build": "rimraf build && tsc -p tsconfig.json"}, "keywords": ["node", "node.js", "typescript", "boilerplate", "express", "rest", "api", "prisma", "postgresql", "es6", "es7", "es8", "es9", "docker", "passport", "joi", "eslint", "prettier"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@faker-js/faker": "^7.6.0", "@jest/globals": "^29.3.1", "@types/bull": "^4.10.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.13", "@types/express": "^5.0.0", "@types/jest": "^29.2.5", "@types/libsodium-wrappers": "^0.7.11", "@types/morgan": "^1.9.3", "@types/node": "18", "@types/passport": "1", "@types/passport-jwt": "^3.0.7", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/xss-filters": "^0.0.27", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "cross-env": "^7.0.3", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.2", "jest": "^29.3.1", "lint-staged": "^13.1.0", "node-mocks-http": "^1.12.1", "nodemon": "^2.0.20", "prettier": "^2.8.1", "prisma": "^4.10.1", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.5", "swagger-ui-express": "^4.6.0", "tronweb-typings": "^1.0.1", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^5.0.0"}, "dependencies": {"@dip1059/safe-math-js": "^1.0.0", "@metaplex-foundation/mpl-token-metadata": "^3.2.1", "@metaplex-foundation/umi": "^0.9.1", "@noble/secp256k1": "^2.2.3", "@prisma/client": "^4.16.2", "@solana/spl-token": "^0.4.12", "@solana/web3.js": "^1.98.0", "@types/bcryptjs": "^2.4.2", "@types/nodemailer": "^6.4.7", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "bignumber.js": "^9.1.2", "body-parser": "^1.20.2", "bs58": "^5.0.0", "bull": "^4.11.3", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^6.7.0", "helmet": "^6.0.1", "http-status": "^1.5.3", "joi": "^17.7.0", "jsonwebtoken": "^9.0.1", "libsodium-wrappers": "^0.7.13", "moment": "^2.29.4", "morgan": "^1.10.0", "nodemailer": "^6.8.0", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pm2": "^5.2.2", "rimraf": "^6.0.1", "ton": "^13.9.0", "ton-core": "^0.53.0", "ton-crypto": "^3.2.0", "trongrid": "^1.2.6", "tronweb": "^5.3.0", "web3": "^3.0.0-rc.5", "winston": "^3.8.2", "xss-filters": "^1.2.7"}}