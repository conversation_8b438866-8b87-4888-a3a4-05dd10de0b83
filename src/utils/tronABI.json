{"entrys": [{"outputs": [{"type": "string"}], "constant": true, "name": "name", "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "_upgraded<PERSON>ddress", "type": "address"}], "name": "deprecate", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "bool"}], "inputs": [{"name": "_spender", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "approve", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "bool"}], "constant": true, "name": "deprecated", "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "_evilUser", "type": "address"}], "name": "addBlackList", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "name": "totalSupply", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "bool"}], "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "transferFrom", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "address"}], "constant": true, "name": "upgradedAddress", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "uint8"}], "constant": true, "name": "decimals", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "name": "maximumFee", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "name": "_totalSupply", "stateMutability": "view", "type": "function"}, {"name": "unpause", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "bool"}], "constant": true, "inputs": [{"name": "_maker", "type": "address"}], "name": "getBlackListStatus", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "bool"}], "constant": true, "name": "paused", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "bool"}], "inputs": [{"name": "_spender", "type": "address"}, {"name": "_subtractedValue", "type": "uint256"}], "name": "decreaseApproval", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "inputs": [{"name": "who", "type": "address"}], "name": "balanceOf", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "inputs": [{"name": "_value", "type": "uint256"}], "name": "calcFee", "stateMutability": "view", "type": "function"}, {"name": "pause", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "address"}], "constant": true, "name": "owner", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "string"}], "constant": true, "name": "symbol", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "bool"}], "inputs": [{"name": "_to", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "transfer", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "inputs": [{"name": "who", "type": "address"}], "name": "oldBalanceOf", "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "newBasisPoints", "type": "uint256"}, {"name": "newMaxFee", "type": "uint256"}], "name": "setParams", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "amount", "type": "uint256"}], "name": "issue", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "bool"}], "inputs": [{"name": "_spender", "type": "address"}, {"name": "_addedValue", "type": "uint256"}], "name": "increaseApproval", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "amount", "type": "uint256"}], "name": "redeem", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"name": "remaining", "type": "uint256"}], "constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "name": "basisPointsRate", "stateMutability": "view", "type": "function"}, {"outputs": [{"type": "bool"}], "constant": true, "inputs": [{"type": "address"}], "name": "isBlackListed", "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "_clearedUser", "type": "address"}], "name": "removeBlackList", "stateMutability": "nonpayable", "type": "function"}, {"outputs": [{"type": "uint256"}], "constant": true, "name": "MAX_UINT", "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_blackListedUser", "type": "address"}], "name": "destroyBlackFunds", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_initialSupply", "type": "uint256"}, {"name": "_name", "type": "string"}, {"name": "_symbol", "type": "string"}, {"name": "_decimals", "type": "uint8"}], "stateMutability": "Nonpayable", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"indexed": true, "name": "_blackListedUser", "type": "address"}, {"name": "_balance", "type": "uint256"}], "name": "DestroyedBlackFunds", "type": "Event"}, {"inputs": [{"name": "amount", "type": "uint256"}], "name": "Issue", "type": "Event"}, {"inputs": [{"name": "amount", "type": "uint256"}], "name": "Redeem", "type": "Event"}, {"inputs": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "Deprecate", "type": "Event"}, {"inputs": [{"indexed": true, "name": "_user", "type": "address"}], "name": "AddedBlackList", "type": "Event"}, {"inputs": [{"indexed": true, "name": "_user", "type": "address"}], "name": "RemovedBlackList", "type": "Event"}, {"inputs": [{"name": "feeBasisPoints", "type": "uint256"}, {"name": "maxFee", "type": "uint256"}], "name": "Params", "type": "Event"}, {"name": "Pause", "type": "Event"}, {"name": "Unpause", "type": "Event"}, {"inputs": [{"indexed": true, "name": "previousOwner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "Event"}, {"inputs": [{"indexed": true, "name": "owner", "type": "address"}, {"indexed": true, "name": "spender", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "Approval", "type": "Event"}, {"inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "to", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "Transfer", "type": "Event"}]}