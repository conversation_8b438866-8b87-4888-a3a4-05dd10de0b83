# Wallet Balance Tracking Feature

این فیچر برای ردیابی موجودی کیف پول‌ها و بروزرسانی خودکار آن‌ها طراحی شده است.

## 🎯 ویژگی‌ها

- **بروزرسانی خودکار موجودی**: هر 5 دقیقه موجودی تمام کیف پول‌ها بررسی و بروزرسانی می‌شود
- **پشتیبانی از شبکه‌های مختلف**: TRON (TRX/TRC20), EVM (ETH/ERC20), Solana, TON
- **API endpoints**: برای مدیریت دستی و مشاهده موجودی‌ها
- **آمار و گزارش**: نمایش آمار کلی موجودی‌ها

## 📊 تغییرات دیتابیس

### جدول `wallet_address_histories`

دو ستون جدید اضافه شده:

```sql
-- اضافه کردن ستون‌های ردیابی موجودی
ALTER TABLE wallet_address_histories 
ADD COLUMN current_balance DECIMAL(29, 18) DEFAULT 0.000000000000000000,
ADD COLUMN last_balance_update TIMESTAMP NULL;

-- ایجاد ایندکس برای بهبود عملکرد
CREATE INDEX idx_wallet_address_histories_balance_update ON wallet_address_histories(last_balance_update);
CREATE INDEX idx_wallet_address_histories_network_coin ON wallet_address_histories(network_id, coin_type);
```

## 🔧 نصب و راه‌اندازی

### 1. اجرای Migration دیتابیس

```bash
# اجرای فایل SQL دستی
mysql -u your_username -p your_database < manual_migration_wallet_balance.sql
```

### 2. فعال‌سازی Cron Jobs

Cron job به صورت خودکار در `src/index.ts` فعال شده و هر 5 دقیقه اجرا می‌شود.

### 3. تست عملکرد

```bash
# ساخت پروژه
npm run build

# تست دستی
node test-balance-update.js
```

## 🌐 API Endpoints

### بروزرسانی موجودی‌ها

```http
POST /v1/balance/update-all
Authorization: Bearer <token>
```

### بروزرسانی موجودی شبکه خاص

```http
POST /v1/balance/update-network/{networkId}
Authorization: Bearer <token>
```

### مشاهده موجودی کیف پول خاص

```http
GET /v1/balance/wallet/{address}
Authorization: Bearer <token>
```

### لیست تمام موجودی‌ها

```http
GET /v1/balance/wallets?page=1&limit=50&network_id=1&coin_type=TRX
Authorization: Bearer <token>
```

### آمار موجودی‌ها

```http
GET /v1/balance/statistics
Authorization: Bearer <token>
```

## 📁 فایل‌های اضافه شده

- `src/services/balance-update.service.ts` - سرویس اصلی بروزرسانی موجودی
- `src/controllers/balance.controller.ts` - کنترلر API endpoints
- `src/routes/v1/balance.route.ts` - تعریف route های API
- `src/cron/update-wallet-balances.ts` - Cron job بروزرسانی خودکار
- `manual_migration_wallet_balance.sql` - Migration دستی دیتابیس

## 🔄 نحوه کارکرد

### 1. بروزرسانی خودکار (هر 5 دقیقه)

```javascript
// در src/cron/index.ts
setInterval(async () => {
  console.log('Running wallet balance update job...');
  await updateWalletBalances();
}, 5 * 60 * 1000); // 5 دقیقه
```

### 2. بررسی موجودی بر اساس نوع شبکه

- **TRON**: استفاده از `getTrxBalance` و `getTrc20TokenBalance`
- **EVM**: استفاده از `getEthBalance` و `getEthTokenBalance`
- **Solana**: آماده برای پیاده‌سازی آینده
- **TON**: آماده برای پیاده‌سازی آینده

### 3. ذخیره در دیتابیس

```sql
UPDATE wallet_address_histories 
SET current_balance = ?, 
    last_balance_update = NOW(), 
    updated_at = NOW() 
WHERE id = ?
```

## 🛠️ عیب‌یابی

### اگر ستون‌های جدید وجود ندارند

سیستم به صورت خودکار تشخیص می‌دهد و:
- موجودی را در لاگ نمایش می‌دهد
- فقط `updated_at` را بروزرسانی می‌کند
- خطای مهلک ایجاد نمی‌کند

### مشاهده لاگ‌ها

```bash
# مشاهده لاگ‌های بروزرسانی موجودی
tail -f logs/app.log | grep "balance"
```

### تست دستی یک شبکه خاص

```bash
curl -X POST "http://localhost:3000/v1/balance/update-network/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "evmapisecret: YOUR_API_SECRET"
```

## 📈 مزایا

1. **کارایی بالا**: بررسی مستقیم کیف پول‌ها به جای اسکن بلاک‌ها
2. **قابلیت اطمینان**: مکانیزم fallback برای خطاها
3. **مقیاس‌پذیری**: پشتیبانی از شبکه‌های متعدد
4. **مانیتورینگ**: API های کامل برای نظارت و مدیریت

## 🔮 توسعه‌های آینده

- [ ] پشتیبانی کامل از Solana و TON
- [ ] اعلان‌های تغییر موجودی
- [ ] تاریخچه تغییرات موجودی
- [ ] تنظیمات فاصله زمانی بروزرسانی
- [ ] گزارش‌گیری پیشرفته
