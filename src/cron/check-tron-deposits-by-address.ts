import { PrismaClient } from '@prisma/client';
import { checkTronDepositsByAddresses } from '../services/evm/deposit.service';
import { TRON_BASE_COIN } from '../utils/coreConstant';

const prisma = new PrismaClient();

/**
 * Cron job to check TRON deposits by directly scanning wallet addresses
 * This is more efficient than scanning all blocks
 */
const checkTronDepositsByAddress = async () => {
  try {
    console.log("Starting TRON deposit check by address scan...");
    
    // Get all active TRON networks
    const networks = await prisma.networks.findMany({
      where: {
        base_type: TRON_BASE_COIN,
        status: 1
      }
    });
    
    if (!networks || networks.length === 0) {
      console.log("No active TRON networks found");
      return;
    }
    
    console.log(`Found ${networks.length} active TRON networks`);
    
    // Process each network
    for (const network of networks) {
      console.log(`Processing network: ${network.name} (ID: ${network.id})`);
      
      if (!network.rpc_url) {
        console.log(`Network ${network.name} has no RPC URL, skipping`);
        continue;
      }
      
      // Check deposits for this network
      const result = await checkTronDepositsByAddresses(network.rpc_url, Number(network.id));
      
      if (result.success) {
        console.log(`Successfully checked deposits for network ${network.name}`);
        if (result.data && result.data.length > 0) {
          console.log(`Found ${result.data.length} new deposits for network ${network.name}`);
        } else {
          console.log(`No new deposits found for network ${network.name}`);
        }
      } else {
        console.error(`Error checking deposits for network ${network.name}: ${result.message}`);
      }
    }
    
    console.log("TRON deposit check by address scan completed");
  } catch (err: any) {
    console.error("Error in TRON deposit check by address scan:", err.stack || err);
  }
};

// Export the function for cron job
export { checkTronDepositsByAddress };

// If this file is run directly, execute the function
if (require.main === module) {
  checkTronDepositsByAddress()
    .then(() => {
      console.log("TRON deposit check completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("TRON deposit check failed:", error);
      process.exit(1);
    });
}
