import express from 'express';
import authRoute from './auth.route';
import evmRoute from './wallet.route';
import testRoute from './test.route';
import balanceRoute from './balance.route';


const router = express.Router();

const defaultRoutes = [
  {
    path: "/auth",
    route: authRoute,
  },
  {
    path: "/evm",
    route: evmRoute,
  },
  {
    path: "/test",
    route: testRoute,
  },
  {
    path: "/balance",
    route: balanceRoute,
  },
];



defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

/* istanbul ignore next */


export default router;
